"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { useTranslations } from "next-intl";

export default function CTASection() {
  const tCTA = useTranslations("CTA");
  const tCommon = useTranslations("Common");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      // Simulate form submission
      setIsSubmitted(true);
      setEmail("");
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="cta" ref={ref}>
      <div className="container">
        <motion.div
          className="cta-content"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          <h2 className="cta-title">{tCTA("title")}</h2>
          <p className="cta-subtitle">{tCTA("subtitle")}</p>

          {!isSubmitted ? (
            <form className="cta-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={tCTA("emailPlaceholder")}
                  className="email-input"
                  required
                />
                <button type="submit" className="btn-primary btn-large">
                  {tCommon("getEarlyAccess")}
                </button>
              </div>
              <p className="form-note">{tCTA("note")}</p>
            </form>
          ) : (
            <div className="success-message">
              <h3>Thanks for joining!</h3>
              <p>We'll notify you when PersonaRoll launches.</p>
            </div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
